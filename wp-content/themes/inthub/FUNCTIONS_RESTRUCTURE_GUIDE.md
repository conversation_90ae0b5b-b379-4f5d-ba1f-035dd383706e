# Hướng dẫn Tái cấu trúc Functions.php - IntHub Theme

## Tổng quan

File `functions.php` gốc có **3716 dòng** đã được tái cấu trúc thành các module nhỏ hơn để dễ bảo trì và phát triển. Cấu trúc mới tuân theo các best practices của WordPress theme development.

## Cấu trúc thư mục mới

```
wp-content/themes/inthub/
├── functions.php (file chính - chỉ chứa includes)
├── inc/
│   ├── theme-setup.php              # Cấu hình cơ bản theme
│   ├── enqueue-scripts.php          # Load CSS/JS files
│   ├── widgets.php                  # Widget areas và custom widgets
│   ├── customizer.php               # Theme customizer settings
│   ├── navigation.php               # Navigation walker và menu functions
│   ├── seo.php                      # SEO và meta functions
│   ├── post-types/                  # Custom Post Types
│   │   ├── testimonials.php
│   │   ├── services.php
│   │   ├── events.php
│   │   ├── universities.php
│   │   ├── scholarships.php
│   │   └── nganh-hoc.php
│   ├── taxonomies/                  # Custom Taxonomies
│   │   ├── university-categories.php
│   │   ├── scholarship-categories.php
│   │   └── nganh-hoc-taxonomies.php
│   ├── meta-boxes/                  # Meta boxes (sẽ tạo tiếp)
│   │   ├── event-meta.php
│   │   ├── university-meta.php
│   │   ├── scholarship-meta.php
│   │   └── nganh-hoc-meta.php
│   ├── admin/                       # Admin customizations (sẽ tạo tiếp)
│   │   ├── admin-columns.php
│   │   ├── admin-filters.php
│   │   └── dashboard-widgets.php
│   ├── ajax/                        # AJAX functions (sẽ tạo tiếp)
│   │   ├── load-more-universities.php
│   │   └── load-more-scholarships.php
│   └── helpers/                     # Helper functions (sẽ tạo tiếp)
│       ├── university-helpers.php
│       ├── scholarship-helpers.php
│       └── general-helpers.php
```

## Các file đã được tạo

### 1. **inc/theme-setup.php**
- Theme setup và cấu hình cơ bản
- Custom excerpt length và more text
- Custom body classes
- Flush rewrite rules khi activate theme

### 2. **inc/enqueue-scripts.php**
- Enqueue tất cả CSS và JavaScript files
- Conditional loading cho từng post type
- AJAX localization

### 3. **inc/widgets.php**
- Đăng ký widget areas (sidebar, footer)
- Custom widgets: University Categories, Scholarship Categories

### 4. **inc/post-types/** (6 files)
- `testimonials.php` - Testimonials post type
- `services.php` - Services post type  
- `events.php` - Events post type + query functions + structured data
- `universities.php` - Universities post type
- `scholarships.php` - Scholarships post type
- `nganh-hoc.php` - Nganh Hoc post type

### 5. **inc/taxonomies/** (3 files)
- `university-categories.php` - University categories + default terms
- `scholarship-categories.php` - Scholarship categories + default terms
- `nganh-hoc-taxonomies.php` - Linh Vuc & Cap Do Dao Tao taxonomies + default terms

### 6. **inc/customizer.php**
- Theme customizer settings
- Contact information
- Social media links

### 7. **inc/navigation.php**
- Custom navigation walker cho multi-level menus
- Dropdown navigation với Tailwind CSS classes

### 8. **inc/seo.php**
- SEO meta tags cho taxonomies
- Open Graph và Twitter Card tags
- Yoast SEO integration

### 9. **functions-new.php**
- File functions.php mới chỉ chứa includes
- Theme constants
- Compatibility functions

## Cách triển khai

### Bước 1: Backup
```bash
# Backup file functions.php gốc
cp functions.php functions-backup.php
```

### Bước 2: Thay thế file chính
```bash
# Thay thế functions.php bằng file mới
mv functions-new.php functions.php
```

### Bước 3: Kiểm tra
1. Truy cập WordPress admin
2. Kiểm tra các post types có hiển thị đúng không
3. Kiểm tra taxonomies
4. Test frontend

## Lợi ích của cấu trúc mới

### 1. **Dễ bảo trì**
- Mỗi file có chức năng cụ thể
- Dễ tìm và sửa lỗi
- Code được tổ chức logic

### 2. **Hiệu suất tốt hơn**
- Chỉ load những gì cần thiết
- Conditional loading cho từng trang

### 3. **Collaboration tốt hơn**
- Nhiều developer có thể làm việc song song
- Ít conflict khi merge code

### 4. **Dễ mở rộng**
- Thêm tính năng mới dễ dàng
- Không ảnh hưởng đến code hiện có

### 5. **Testing dễ hơn**
- Test từng module riêng biệt
- Debug nhanh hơn

## Các file cần tạo tiếp

### Meta Boxes
- `inc/meta-boxes/event-meta.php`
- `inc/meta-boxes/university-meta.php`
- `inc/meta-boxes/scholarship-meta.php`
- `inc/meta-boxes/nganh-hoc-meta.php`

### Admin Functions
- `inc/admin/admin-columns.php`
- `inc/admin/admin-filters.php`
- `inc/admin/dashboard-widgets.php`

### AJAX Functions
- `inc/ajax/load-more-universities.php`
- `inc/ajax/load-more-scholarships.php`

### Helper Functions
- `inc/helpers/university-helpers.php`
- `inc/helpers/scholarship-helpers.php`
- `inc/helpers/general-helpers.php`

### Security & Performance
- `inc/security.php`
- `inc/performance.php`

## Best Practices được áp dụng

1. **Separation of Concerns** - Mỗi file có một mục đích cụ thể
2. **DRY Principle** - Không lặp lại code
3. **WordPress Coding Standards** - Tuân theo chuẩn WordPress
4. **Security First** - Kiểm tra permissions và sanitize data
5. **Performance Optimization** - Conditional loading và caching
6. **Documentation** - Comment rõ ràng cho từng function

## Troubleshooting

### Nếu gặp lỗi "Function not found"
1. Kiểm tra file có được include đúng không
2. Kiểm tra tên function có đúng không
3. Kiểm tra thứ tự include files

### Nếu post types không hiển thị
1. Vào Settings > Permalinks và click Save
2. Kiểm tra function registration có được gọi không

### Nếu CSS/JS không load
1. Kiểm tra đường dẫn file
2. Kiểm tra conditional loading logic
3. Clear cache nếu có

## Kết luận

Việc tái cấu trúc này giúp theme IntHub trở nên:
- **Maintainable** - Dễ bảo trì
- **Scalable** - Dễ mở rộng  
- **Readable** - Dễ đọc hiểu
- **Professional** - Tuân theo best practices

Cấu trúc mới này sẽ giúp team phát triển hiệu quả hơn và theme sẽ ổn định hơn trong tương lai.
